// Developer Settings Page JavaScript
class DeveloperSettingsManager {
    constructor() {
        this.currentSection = 'announcements';
        this.announcements = [];
        this.currentFilter = 'all';
        this.tinymceEditor = null;

        this.initializeEventListeners();
        this.initializeTinyMCE();
        this.loadAnnouncementData();
    }

    initializeEventListeners() {
        // Navigation items
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = item.dataset.section;
                this.switchSection(section);
            });
        });

        // Create announcement button
        document.getElementById('createAnnouncementBtn').addEventListener('click', () => {
            this.showCreateAnnouncementModal();
        });

        // Filter buttons
        document.querySelectorAll('.filter-buttons .btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setAnnouncementFilter(e.target.dataset.filter);
            });
        });

        // Modal buttons
        document.getElementById('saveAsDraftBtn').addEventListener('click', () => {
            this.saveAnnouncement('draft');
        });

        document.getElementById('publishAnnouncementBtn').addEventListener('click', () => {
            this.saveAnnouncement('published');
        });

        // Search functionality
        document.getElementById('announcementSearch').addEventListener('input', (e) => {
            this.searchAnnouncements(e.target.value);
        });
    }

    initializeTinyMCE() {
        if (typeof tinymce !== 'undefined') {
            tinymce.init({
                selector: '#announcementContent',
                height: 300,
                menubar: false,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount'
                ],
                toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
                content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                setup: (editor) => {
                    this.tinymceEditor = editor;
                }
            });
        }
    }

    switchSection(section) {
        // Update navigation
        document.querySelectorAll('.settings-nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.settings-section').forEach(sec => {
            sec.classList.remove('active');
            sec.style.display = 'none';
        });

        const targetSection = document.getElementById(section);
        if (targetSection) {
            targetSection.classList.add('active');
            targetSection.style.display = 'block';
        }

        this.currentSection = section;

        // Load section-specific data
        if (section === 'announcements') {
            this.loadAnnouncementData();
        } else if (section === 'system-monitoring') {
            this.loadSystemMonitoring();
        }
    }

    async loadAnnouncementData() {
        try {
            // Load announcements
            const response = await fetch('/api/announcements/admin');
            const data = await response.json();

            if (data.success) {
                this.announcements = data.data;
                this.renderAnnouncementsTable();
            }

            // Load stats
            const statsResponse = await fetch('/api/announcements/admin/stats');
            const statsData = await statsResponse.json();

            if (statsData.success) {
                this.updateAnnouncementStats(statsData.data);
            }
        } catch (error) {
            console.error('Error loading announcement data:', error);
            this.showErrorMessage('Failed to load announcement data');
        }
    }

    updateAnnouncementStats(stats) {
        document.getElementById('totalAnnouncements').textContent = stats.total;
        document.getElementById('activeAnnouncements').textContent = stats.active;

        // Calculate drafts and pinned from breakdown
        const drafts = stats.breakdown.find(b => b.status === 'draft')?._count?.id || 0;
        document.getElementById('draftAnnouncements').textContent = drafts;

        // For pinned, we'd need to modify the API to include this info
        document.getElementById('pinnedAnnouncements').textContent = '0'; // Placeholder
    }

    renderAnnouncementsTable() {
        const tbody = document.getElementById('announcementsTableBody');
        const loading = document.getElementById('announcementsLoading');
        const empty = document.getElementById('announcementsEmpty');

        loading.style.display = 'none';

        if (this.announcements.length === 0) {
            empty.style.display = 'block';
            tbody.innerHTML = '';
            return;
        }

        empty.style.display = 'none';

        let filteredAnnouncements = this.announcements;
        if (this.currentFilter !== 'all') {
            filteredAnnouncements = this.announcements.filter(a => a.status === this.currentFilter);
        }

        tbody.innerHTML = filteredAnnouncements.map(announcement => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        ${announcement.is_pinned ? '<i class="fas fa-thumbtack text-warning me-2"></i>' : ''}
                        <div>
                            <div class="fw-semibold">${this.escapeHtml(announcement.title)}</div>
                            ${announcement.summary ? `<small class="text-muted">${this.escapeHtml(announcement.summary)}</small>` : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="type-badge ${announcement.type}">${announcement.type}</span>
                </td>
                <td>
                    <span class="status-badge ${announcement.status}">${announcement.status}</span>
                </td>
                <td>
                    <span class="target-badge">${announcement.target_audience}</span>
                </td>
                <td>
                    <small>${this.formatDate(announcement.created_at)}</small>
                </td>
                <td>
                    <div class="d-flex gap-1">
                        <button class="action-btn edit" onclick="developerSettings.editAnnouncement(${announcement.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${announcement.status === 'draft' ? `
                            <button class="action-btn publish" onclick="developerSettings.publishAnnouncement(${announcement.id})" title="Publish">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        ` : ''}
                        <button class="action-btn delete" onclick="developerSettings.deleteAnnouncement(${announcement.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    setAnnouncementFilter(filter) {
        // Update filter buttons
        document.querySelectorAll('.filter-buttons .btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

        this.currentFilter = filter;
        this.renderAnnouncementsTable();
    }

    showCreateAnnouncementModal() {
        this.resetAnnouncementForm();
        document.getElementById('announcementModalTitle').textContent = 'Create Announcement';
        new bootstrap.Modal(document.getElementById('announcementModal')).show();
    }

    editAnnouncement(id) {
        const announcement = this.announcements.find(a => a.id === id);
        if (!announcement) return;

        this.populateAnnouncementForm(announcement);
        document.getElementById('announcementModalTitle').textContent = 'Edit Announcement';
        new bootstrap.Modal(document.getElementById('announcementModal')).show();
    }

    resetAnnouncementForm() {
        document.getElementById('announcementForm').reset();
        document.getElementById('announcementId').value = '';
        if (this.tinymceEditor) {
            this.tinymceEditor.setContent('');
        }
    }

    populateAnnouncementForm(announcement) {
        document.getElementById('announcementId').value = announcement.id;
        document.getElementById('announcementTitle').value = announcement.title;
        document.getElementById('announcementSummary').value = announcement.summary || '';
        document.getElementById('announcementType').value = announcement.type;
        document.getElementById('announcementPriority').value = announcement.priority;
        document.getElementById('announcementAudience').value = announcement.target_audience;
        document.getElementById('announcementPinned').checked = announcement.is_pinned;
        document.getElementById('announcementPopup').checked = announcement.is_popup;

        if (announcement.publish_at) {
            const publishDate = new Date(announcement.publish_at);
            document.getElementById('announcementPublishDate').value = publishDate.toISOString().slice(0, 16);
        }

        if (announcement.expires_at) {
            const expiryDate = new Date(announcement.expires_at);
            document.getElementById('announcementExpiryDate').value = expiryDate.toISOString().slice(0, 16);
        }

        if (this.tinymceEditor) {
            this.tinymceEditor.setContent(announcement.content);
        }
    }

    async saveAnnouncement(status) {
        const formData = this.getAnnouncementFormData();
        formData.status = status;

        const id = document.getElementById('announcementId').value;
        const isEdit = id !== '';

        try {
            const url = isEdit ? `/api/announcements/${id}` : '/api/announcements';
            const method = isEdit ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccessMessage(`Announcement ${isEdit ? 'updated' : 'created'} successfully`);
                bootstrap.Modal.getInstance(document.getElementById('announcementModal')).hide();
                this.loadAnnouncementData();
            } else {
                this.showErrorMessage(data.message || 'Failed to save announcement');
            }
        } catch (error) {
            console.error('Error saving announcement:', error);
            this.showErrorMessage('Failed to save announcement');
        }
    }

    getAnnouncementFormData() {
        const content = this.tinymceEditor ? this.tinymceEditor.getContent() : document.getElementById('announcementContent').value;

        return {
            title: document.getElementById('announcementTitle').value,
            summary: document.getElementById('announcementSummary').value,
            content: content,
            type: document.getElementById('announcementType').value,
            priority: document.getElementById('announcementPriority').value,
            targetAudience: document.getElementById('announcementAudience').value,
            isPinned: document.getElementById('announcementPinned').checked,
            isPopup: document.getElementById('announcementPopup').checked,
            publishAt: document.getElementById('announcementPublishDate').value || null,
            expiresAt: document.getElementById('announcementExpiryDate').value || null
        };
    }

    async publishAnnouncement(id) {
        if (!confirm('Are you sure you want to publish this announcement?')) return;

        try {
            const response = await fetch(`/api/announcements/${id}/publish`, {
                method: 'PUT'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccessMessage('Announcement published successfully');
                this.loadAnnouncementData();
            } else {
                this.showErrorMessage(data.message || 'Failed to publish announcement');
            }
        } catch (error) {
            console.error('Error publishing announcement:', error);
            this.showErrorMessage('Failed to publish announcement');
        }
    }

    async deleteAnnouncement(id) {
        if (!confirm('Are you sure you want to delete this announcement? This action cannot be undone.')) return;

        try {
            const response = await fetch(`/api/announcements/${id}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccessMessage('Announcement deleted successfully');
                this.loadAnnouncementData();
            } else {
                this.showErrorMessage(data.message || 'Failed to delete announcement');
            }
        } catch (error) {
            console.error('Error deleting announcement:', error);
            this.showErrorMessage('Failed to delete announcement');
        }
    }

    searchAnnouncements(query) {
        // Simple client-side search
        const rows = document.querySelectorAll('#announcementsTableBody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }

    loadSystemMonitoring() {
        // Placeholder for system monitoring functionality
        console.log('Loading system monitoring data...');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showSuccessMessage(message) {
        // You can implement a toast notification system here
        alert(message); // Temporary implementation
    }

    showErrorMessage(message) {
        // You can implement a toast notification system here
        alert(message); // Temporary implementation
    }
}

// Global functions for external calls
function showCreateAnnouncementModal() {
    if (window.developerSettings) {
        window.developerSettings.showCreateAnnouncementModal();
    }
}

function updateHelpUrl() {
    const url = document.getElementById('helpUrl').value;
    // Implement help URL update functionality
    alert('Help URL updated: ' + url);
}

function openHelpEditor() {
    // Implement help editor functionality
    window.open('/help', '_blank');
}

function previewHelp() {
    // Implement help preview functionality
    window.open('/help', '_blank');
}

function savePortalSettings() {
    const settings = {
        api: {
            rateLimit: document.getElementById('apiRateLimit').value,
            timeout: document.getElementById('apiTimeout').value,
            retryAttempts: document.getElementById('retryAttempts').value
        },
        security: {
            sessionTimeout: document.getElementById('sessionTimeout').value,
            maxLoginAttempts: document.getElementById('maxLoginAttempts').value,
            lockoutDuration: document.getElementById('lockoutDuration').value,
            enforceHttps: document.getElementById('enforceHttps').checked,
            enableTwoFactor: document.getElementById('enableTwoFactor').checked
        },
        dataManagement: {
            logRetention: document.getElementById('logRetention').value,
            notificationRetention: document.getElementById('notificationRetention').value,
            fileCleanup: document.getElementById('fileCleanup').value,
            autoBackup: document.getElementById('autoBackup').checked,
            compressLogs: document.getElementById('compressLogs').checked
        },
        email: {
            smtpServer: document.getElementById('smtpServer').value,
            smtpPort: document.getElementById('smtpPort').value,
            smtpEncryption: document.getElementById('smtpEncryption').value,
            fromEmail: document.getElementById('fromEmail').value,
            adminEmail: document.getElementById('adminEmail').value
        },
        maintenance: {
            mode: document.getElementById('maintenanceMode').value,
            start: document.getElementById('maintenanceStart').value,
            end: document.getElementById('maintenanceEnd').value,
            message: document.getElementById('maintenanceMessage').value
        }
    };

    // Here you would typically send this to your API
    console.log('Saving portal settings:', settings);
    alert('Portal settings saved successfully!');
}

function resetPortalSettings() {
    if (!confirm('Are you sure you want to reset all settings to defaults?')) return;

    // Reset to default values
    document.getElementById('apiRateLimit').value = '300';
    document.getElementById('apiTimeout').value = '30';
    document.getElementById('retryAttempts').value = '3';
    document.getElementById('sessionTimeout').value = '30';
    document.getElementById('maxLoginAttempts').value = '5';
    document.getElementById('lockoutDuration').value = '15';
    document.getElementById('enforceHttps').checked = true;
    document.getElementById('enableTwoFactor').checked = false;
    document.getElementById('logRetention').value = '90';
    document.getElementById('notificationRetention').value = '30';
    document.getElementById('fileCleanup').value = '90';
    document.getElementById('autoBackup').checked = true;
    document.getElementById('compressLogs').checked = false;
    document.getElementById('smtpServer').value = '';
    document.getElementById('smtpPort').value = '587';
    document.getElementById('smtpEncryption').value = 'tls';
    document.getElementById('fromEmail').value = '';
    document.getElementById('adminEmail').value = '';
    document.getElementById('maintenanceMode').value = 'off';
    document.getElementById('maintenanceStart').value = '';
    document.getElementById('maintenanceEnd').value = '';
    document.getElementById('maintenanceMessage').value = '';

    alert('Settings reset to defaults');
}

// Initialize developer settings manager when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.developerSettings = new DeveloperSettingsManager();
});

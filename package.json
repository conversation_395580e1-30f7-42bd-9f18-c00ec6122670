{"name": "pxceinvoiceportal", "version": "1.1.3", "main": "server.js", "type": "commonjs", "scripts": {"test": "jest", "dev": "cross-env NODE_ENV=development EXCEL_DEBUG=false nodemon server.js --ignore-watch 'logs/' --ignore-watch '*.log' --ignore-watch 'generated/' --ignore-watch 'temp/' --ignore-watch 'excel/' --ignore-watch 'output/' --ignore-watch 'config/'", "start": "nodemon server.js --ignore-watch 'logs/' --ignore-watch '*.log' --ignore-watch 'generated/' --ignore-watch 'temp/' --ignore-watch 'excel/' --ignore-watch 'output/' --ignore-watch 'config/'", "pm2": "pm2 start server.js --name eInvoice", "version:major": "npm version major", "version:minor": "npm version minor", "version:patch": "npm version patch"}, "keywords": ["eInvoice", "LHDN", "SDK", "IRBM", "Pixelcare Consulting", "Middleware"], "author": "Pixelcare Consulting", "license": "ISC", "description": "A specialized middleware solution designed to integrate business applications with LHDN's (Lembaga Hasil Dalam Negeri) e-Invoicing system. This middleware facilitates seamless invoice data exchange while ensuring compliance with Malaysian tax regulations.", "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.7.2", "@google/generative-ai": "^0.23.0", "@prisma/client": "^6.7.0", "axios": "^1.7.9", "axios-retry": "^4.5.0", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "canvas": "^3.1.0", "chok": "^0.2.0", "connect-session-sequelize": "^7.1.7", "connect-sqlite3": "^0.9.15", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "feather-icons": "^4.29.2", "helmet": "^8.0.0", "http": "^0.0.1-security", "http-proxy-middleware": "^3.0.3", "ini": "^1.3.5", "jquery": "^3.7.1", "js2xmlparser": "^5.0.0", "jsonminify": "^0.4.2", "jsonwebtoken": "^9.0.2", "jsrender": "^1.0.15", "jsreport-browser-client": "^2.2.2", "jsreport-chrome-pdf": "^1.10.0", "jsreport-core": "^1.5.1", "jsreport-jsrender": "^2.1.1", "memory-cache": "^0.2.0", "moment": "^2.30.1", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mustache": "^3.1.0", "mysql2": "^3.12.0", "node-cache": "^5.1.2", "node-fetch": "^2.7.0", "node-forge": "^1.3.1", "os": "^0.1.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "prettyjson": "^1.2.5", "puppeteer": "^23.11.1", "qrcode": "^1.5.4", "request": "^2.88.2", "session-file-store": "^1.5.0", "smb2": "^0.2.11", "socket.io": "^4.8.1", "ssh2-sftp-client": "^11.0.0", "sweetalert2": "^11.6.13", "swig": "^1.4.2", "swig-templates": "^2.0.3", "tedious": "^18.6.1", "umzug": "^3.8.2", "winston": "^3.17.0", "xlsx": "^0.18.5", "xml-crypto": "^6.0.0", "xml2js": "^0.6.2", "xmldom": "^0.6.0"}, "devDependencies": {"@fullhuman/postcss-purgecss": "^7.0.2", "@tailwindcss/cli": "^4.0.2", "@tailwindcss/postcss": "^4.0.2", "autoprefixer": "^10.4.17", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "jest": "^29.7.0", "nodemon": "^3.1.7", "postcss": "^8.4.35", "postcss-cli": "^11.0.0", "prisma": "6.8.2", "tailwindcss": "^3.4.1"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"]}, "pnpm": {"onlyBuiltDependencies": ["@parcel/watcher", "@prisma/client", "@prisma/engines", "canvas", "core-js", "cpu-features", "es5-ext", "esbuild", "prisma", "puppeteer", "sharp", "sqlite3", "ssh2", "uglifyjs-webpack-plugin"]}}